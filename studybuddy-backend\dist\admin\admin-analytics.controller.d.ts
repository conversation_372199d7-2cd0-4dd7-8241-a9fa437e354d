import { AdminAnalyticsService } from './admin-analytics.service';
export declare class AdminAnalyticsController {
    private readonly adminAnalyticsService;
    constructor(adminAnalyticsService: AdminAnalyticsService);
    getStudentAnalytics(userId: string): Promise<{
        studentInfo: {
            userId: import("mongoose").Types.ObjectId;
            email: string;
            name: any;
            phone: any;
            class: any;
            schoolName: any;
            profileImage: any;
            createdAt: any;
            subjects: any;
        };
        analytics: {
            quizStats: {
                totalAttempted: number;
                accuracy: number;
                subjectWiseAttempts: {
                    Mathematics: number;
                    Physics: number;
                    Chemistry: number;
                    Biology: number;
                };
                averageScores: {
                    Mathematics: number;
                    Physics: number;
                    Chemistry: number;
                    Biology: number;
                };
                lastQuizDate: string;
                topicsCompleted: number;
            };
            chatStats: {
                totalMessages: any;
                totalDoubts: any;
                mostDiscussedSubject: string;
                totalTimeSpent: string;
                timeOfDayMostActive: string;
                streak: number;
            };
            leaderboardStats: {
                currentRank: number;
                sparkPoints: number;
                rankMovement: string;
                motivationLevel: string;
            };
            activityPattern: {
                dailyActivity: {
                    date: any;
                    queries: any;
                    timeSpent: number;
                    subjects: any;
                }[];
                weeklyPattern: {
                    Monday: number;
                    Tuesday: number;
                    Wednesday: number;
                    Thursday: number;
                    Friday: number;
                    Saturday: number;
                    Sunday: number;
                };
                monthlyTrend: {
                    date: any;
                    activity: any;
                }[];
            };
        };
    }>;
    downloadStudentReport(userId: string, format?: 'pdf' | 'excel'): Promise<{
        message: string;
        data: {
            studentInfo: {
                userId: import("mongoose").Types.ObjectId;
                email: string;
                name: any;
                phone: any;
                class: any;
                schoolName: any;
                profileImage: any;
                createdAt: any;
                subjects: any;
            };
            analytics: {
                quizStats: {
                    totalAttempted: number;
                    accuracy: number;
                    subjectWiseAttempts: {
                        Mathematics: number;
                        Physics: number;
                        Chemistry: number;
                        Biology: number;
                    };
                    averageScores: {
                        Mathematics: number;
                        Physics: number;
                        Chemistry: number;
                        Biology: number;
                    };
                    lastQuizDate: string;
                    topicsCompleted: number;
                };
                chatStats: {
                    totalMessages: any;
                    totalDoubts: any;
                    mostDiscussedSubject: string;
                    totalTimeSpent: string;
                    timeOfDayMostActive: string;
                    streak: number;
                };
                leaderboardStats: {
                    currentRank: number;
                    sparkPoints: number;
                    rankMovement: string;
                    motivationLevel: string;
                };
                activityPattern: {
                    dailyActivity: {
                        date: any;
                        queries: any;
                        timeSpent: number;
                        subjects: any;
                    }[];
                    weeklyPattern: {
                        Monday: number;
                        Tuesday: number;
                        Wednesday: number;
                        Thursday: number;
                        Friday: number;
                        Saturday: number;
                        Sunday: number;
                    };
                    monthlyTrend: {
                        date: any;
                        activity: any;
                    }[];
                };
            };
        };
        downloadUrl: string;
    }>;
    getStudentActivityChart(userId: string, period?: 'week' | 'month' | 'year'): Promise<{
        dailyActivity: {
            date: any;
            queries: any;
            timeSpent: number;
            subjects: any;
        }[];
        subjectDistribution: {
            subject: string;
            percentage: number;
            queries: number;
        }[];
        performanceTrend: {
            date: any;
            accuracy: number;
            rank: number;
        }[];
    }>;
}
