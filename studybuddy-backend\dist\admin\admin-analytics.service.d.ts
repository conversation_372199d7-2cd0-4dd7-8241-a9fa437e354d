import { Model } from 'mongoose';
import { User } from 'src/schemas/user.schema';
import { UserDetails } from 'src/schemas/userDetails.schema';
import { ChatHistory } from 'src/schemas/chatHistory.schema';
export declare class AdminAnalyticsService {
    private userModel;
    private userDetailsModel;
    private chatHistoryModel;
    constructor(userModel: Model<User>, userDetailsModel: Model<UserDetails>, chatHistoryModel: Model<ChatHistory>);
    getStudentAnalytics(userId: string): Promise<{
        studentInfo: {
            userId: import("mongoose").Types.ObjectId;
            email: string;
            name: any;
            phone: any;
            class: any;
            schoolName: any;
            profileImage: any;
            createdAt: any;
            subjects: any;
        };
        analytics: {
            quizStats: {
                totalAttempted: number;
                accuracy: number;
                subjectWiseAttempts: {
                    Mathematics: number;
                    Physics: number;
                    Chemistry: number;
                    Biology: number;
                };
                averageScores: {
                    Mathematics: number;
                    Physics: number;
                    Chemistry: number;
                    Biology: number;
                };
                lastQuizDate: string;
                topicsCompleted: number;
            };
            chatStats: {
                totalMessages: any;
                totalDoubts: any;
                mostDiscussedSubject: string;
                totalTimeSpent: string;
                timeOfDayMostActive: string;
                streak: number;
            };
            leaderboardStats: {
                currentRank: number;
                sparkPoints: number;
                rankMovement: string;
                motivationLevel: string;
            };
            activityPattern: {
                dailyActivity: {
                    date: any;
                    queries: any;
                    timeSpent: number;
                    subjects: any;
                }[];
                weeklyPattern: {
                    Monday: number;
                    Tuesday: number;
                    Wednesday: number;
                    Thursday: number;
                    Friday: number;
                    Saturday: number;
                    Sunday: number;
                };
                monthlyTrend: {
                    date: any;
                    activity: any;
                }[];
            };
        };
    }>;
    private calculateChatStats;
    private calculateQuizStats;
    private calculateLeaderboardStats;
    private calculateActivityPattern;
    private calculateStreak;
    private findMostActiveTime;
    generateStudentReport(userId: string, format?: 'pdf' | 'excel'): Promise<{
        message: string;
        data: {
            studentInfo: {
                userId: import("mongoose").Types.ObjectId;
                email: string;
                name: any;
                phone: any;
                class: any;
                schoolName: any;
                profileImage: any;
                createdAt: any;
                subjects: any;
            };
            analytics: {
                quizStats: {
                    totalAttempted: number;
                    accuracy: number;
                    subjectWiseAttempts: {
                        Mathematics: number;
                        Physics: number;
                        Chemistry: number;
                        Biology: number;
                    };
                    averageScores: {
                        Mathematics: number;
                        Physics: number;
                        Chemistry: number;
                        Biology: number;
                    };
                    lastQuizDate: string;
                    topicsCompleted: number;
                };
                chatStats: {
                    totalMessages: any;
                    totalDoubts: any;
                    mostDiscussedSubject: string;
                    totalTimeSpent: string;
                    timeOfDayMostActive: string;
                    streak: number;
                };
                leaderboardStats: {
                    currentRank: number;
                    sparkPoints: number;
                    rankMovement: string;
                    motivationLevel: string;
                };
                activityPattern: {
                    dailyActivity: {
                        date: any;
                        queries: any;
                        timeSpent: number;
                        subjects: any;
                    }[];
                    weeklyPattern: {
                        Monday: number;
                        Tuesday: number;
                        Wednesday: number;
                        Thursday: number;
                        Friday: number;
                        Saturday: number;
                        Sunday: number;
                    };
                    monthlyTrend: {
                        date: any;
                        activity: any;
                    }[];
                };
            };
        };
        downloadUrl: string;
    }>;
    getStudentActivityChart(userId: string, period?: 'week' | 'month' | 'year'): Promise<{
        dailyActivity: {
            date: any;
            queries: any;
            timeSpent: number;
            subjects: any;
        }[];
        subjectDistribution: {
            subject: string;
            percentage: number;
            queries: number;
        }[];
        performanceTrend: {
            date: any;
            accuracy: number;
            rank: number;
        }[];
    }>;
    private calculateSubjectDistribution;
    private calculatePerformanceTrend;
}
